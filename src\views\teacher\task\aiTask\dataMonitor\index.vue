<script setup lang="ts">
import {
  getConfig,
  getTaskStudentLearningStatistics,
  getTaskStudentRaiseHandList,
  handleQuestion,
  setTaskStudentRaiseHandApi,
} from '@/api/aiTask'
import DataPresentation from '@/views/teacher/task/comprehensiveTask/components/DataPresentation.vue'
import FullscreenCountdown from './components/FullscreenCountdown.vue'
import StudentCard from './components/NewStudentCard.vue'
import ProgressBar from './components/ProgressBar.vue'

/**
 * 学生信息接口
 */
interface IStudentInfo {
  /** 学生ID */
  schoolStudentId: number
  /** 学生姓名 */
  schoolStudentName: string
  /** 头像地址 */
  headpicture?: string
  /** 举手时间 */
  createTime: string
  /** 举手时学习的时间（秒） */
  raiseHandDuration: number
  /** 位置信息 */
  catalogCourseTypeName?: string
  /** 任务ID */
  taskId?: number
  /** 书目录ID */
  bookCatalogId?: number
  /** 章节标签 */
  catalogCourseType?: number
  /** 举手类型:1-AI,2-老师 */
  raiseHandType?: number
  /** 举手状态:1-举手中,2-解答中,3-已解答 */
  raiseHandState?: number
  /** 举手记录ID */
  taskStudentRaiseHandId?: number
}

/**
 * API返回数据接口
 */
interface IRaiseHandResponse {
  /** AI老师举手列表 */
  aiTaskStudentRaiseHandQueryList: IStudentInfo[]
  /** 老师举手列表 */
  teacherTaskStudentRaiseHandQueryList: IStudentInfo[]
}

/**
 * 将秒数转换为 xxhxxm 格式
 * @param seconds 秒数
 * @returns 格式化后的时间字符串，如 "02h30m"
 */
function formatSecondsToHourMinute(seconds: number): {
  hours: string
  minutes: string
} {
  if (!seconds) {
    return {
      hours: '0',
      minutes: '00',
    }
  }
  const duration = $g.dayjs.duration(seconds * 1000) // dayjs的duration接受毫秒
  const hours = Math.floor(duration.asHours())
  const minutes = duration.minutes()

  // 格式化为两位数
  const formattedHours = hours.toString()
  const formattedMinutes = minutes.toString().padStart(2, '0')

  return {
    hours: formattedHours,
    minutes: formattedMinutes,
  }
}

/** 进度条容器是否全屏 */
let isProgressFullScreen = $ref(false)

/** 倒计时总时长（毫秒） */
let totalTime: any = $ref(null) // 整个任务的总时长（结束时间-开始时间）

/** 任务总时长（毫秒）- 用于正确计算百分比 */
let taskTotalDuration: number = $ref(1) // 默认为1防止除以0

/** 当前倒计时剩余时间（毫秒） */
let time = $ref(totalTime)

/** 剩余时间百分比 */
let timePercentage = $ref(100)

/** 任务是否已结束标识 */
let isTaskCompleted = $ref(false)

/** 任务是否已开始标识 */
let isTaskStarted = $ref(false)

/** 任务开始时间字符串 */
let releaseTime = $ref('')

/** 任务完成时间字符串 */
let requireCompleteTime = $ref('')

/** 从路由获取任务ID */
const route = useRoute()
let taskId = $ref(Number(route.query.taskId) || 0)

/** 根据剩余时间百分比计算颜色类名和对应图标 */
const countdownColorClass = computed(() => {
  if (isTaskCompleted) {
    return {
      colorClass: 'text-[#333]', // 任务结束显示绿色
    }
  }
  else if (!isTaskStarted) {
    return {
      colorClass: 'text-[#5BE361]', // 任务未开始显示绿色
      iconSrc: $g.tool.getFileUrl('dataMonitor/time-green.png'),
    }
  }
  else if (timePercentage >= 50) {
    return {
      colorClass: 'text-[#5BE361]', // 绿色
      iconSrc: $g.tool.getFileUrl('dataMonitor/time-green.png'),
    }
  }
  else if (timePercentage > 10) {
    return {
      colorClass: 'text-[#FFA265]', // 橙色
      iconSrc: $g.tool.getFileUrl('dataMonitor/time-yellow.png'),
    }
  }
  else {
    return {
      colorClass: 'text-[#F92A2A]', // 红色
      iconSrc: $g.tool.getFileUrl('dataMonitor/time-red.png'),
    }
  }
})

/** 更新倒计时剩余时间百分比 */
function handleCountdownChange(timeData: any) {
  const remainingTime = timeData.total
  // 避免除以零的情况
  timePercentage =
    taskTotalDuration > 0
      ? Math.floor((remainingTime / taskTotalDuration) * 100)
      : 0

  // 检查倒计时是否结束
  if (remainingTime <= 0) {
    // 判断当前是开始倒计时还是结束倒计时
    if (!isTaskStarted) {
      // 开始倒计时结束，课程开始
      isTaskStarted = true

      // 重新初始化结束倒计时
      const now = $g.dayjs()
      if (requireCompleteTime) {
        const completeTime = $g.dayjs(requireCompleteTime)
        // 计算剩余时间
        totalTime = completeTime.diff(now)
        time = totalTime
        // 计算任务总时长
        taskTotalDuration = completeTime.diff(now)
        // 计算剩余时间百分比
        timePercentage = 100

        // 开始轮询
        startPolling()
      }
    }
    else {
      // 结束倒计时结束，课程完成
      isTaskCompleted = true
      // 任务结束时停止轮询
      stopPolling()
      // 获取一次最终数据
      getTaskStudentRaiseHandListApi(true, true)
    }
  }
}

/**
 * 初始化倒计时时间
 */
function initCountdown() {
  const now = $g.dayjs()

  // 处理任务开始时间
  if (releaseTime) {
    const startTime = $g.dayjs(releaseTime)
    isTaskStarted = now.isAfter(startTime)

    // 如果任务还未开始，显示距离开始的倒计时
    if (!isTaskStarted) {
      totalTime = startTime.diff(now)
      time = totalTime
      timePercentage = 100
      return
    }
  }
  else {
    // 如果没有开始时间，默认为已开始
    isTaskStarted = true
  }

  // 处理任务结束时间
  if (requireCompleteTime) {
    const completeTime = $g.dayjs(requireCompleteTime)
    const startTime = releaseTime ? $g.dayjs(releaseTime) : now

    // 计算任务总时长（用于正确计算百分比）
    taskTotalDuration = completeTime.diff(startTime)

    // 计算剩余时间（毫秒）
    const remaining = completeTime.diff(now)

    // 如果已经超过结束时间
    if (remaining <= 0) {
      isTaskCompleted = true
      time = 0
      timePercentage = 0
      // 课程已结束，不启动轮询，但仍需获取一次初始数据
      stopPolling()
      return
    }

    // 设置剩余时间
    totalTime = remaining
    time = remaining

    // 根据任务总时长计算剩余时间百分比
    timePercentage = Math.floor((remaining / taskTotalDuration) * 100)
  }
}

let dataOption = $ref({
  items: [
    {
      title: '课程完成',
      slotName: 'finishCatalogCount',
    },
    {
      title: '累计提问',
      slotName: 'raiseHandCount',
      unit: '次',
    },
    {
      title: '训练正确率',
      slotName: 'rightRate',
      unit: '%',
    },
    {
      title: '错题订正率',
      slotName: 'errorRate',
      unit: '%',
    },
    {
      title: '学习时长（h/m）',
      slotName: 'avgLearnDuration',
      unit: '',
    },
  ],
  data: {},
})
let raiseHandType = $ref(2) // 默认为老师解答(2)

/** 举手中的学生信息列表 */
let raiseHandStudents = $ref<IStudentInfo[]>([])

/** 已解答的学生信息列表 */
let answeredStudents = $ref<IStudentInfo[]>([])

/** AI举手列表数据缓存 */
let cachedAIData = $ref<IStudentInfo[]>([])

/** 老师举手列表数据缓存 - 未解答 */
let cachedTeacherData = $ref<IStudentInfo[]>([])

/** 老师举手列表数据缓存 - 已解答 */
let cachedTeacherAnsweredData = $ref<IStudentInfo[]>([])

/** 按钮loading状态映射 - taskStudentRaiseHandId为key */
let buttonLoadingMap = $ref<Record<number, boolean>>({})

/** 开关状态 */
let handChecked = $ref(false)

/** 解答上限次数 */
let answerNum = $ref(20)

/** 是否已初始化完成 */
let isInitialized = $ref(false)

/** 是否正在加载举手数据 - 默认为true，确保首次进入就显示loading状态 */
let isLoading = $ref(true)

/** 数据是否已初次加载 */
let isDataLoaded = $ref(false)

/** 轮询定时器ID */
let pollingTimer = $ref<number | null>(null)

/** 轮询计数器，用于强制更新视图 */
let pollCounter = $ref(0)

/* 查询课程概况统计数据 */
function getStatisticsDataApi() {
  getTaskStudentLearningStatistics({
    taskId,
  }).then((res) => {
    dataOption.data = res
  })
}

/**
 * 根据当前选择的标签显示对应的缓存数据
 */
function updateDisplayData() {
  if (raiseHandType === 1) {
    // AI举手列表 - 所有状态都放到一个数组展示
    raiseHandStudents = cachedAIData
    answeredStudents = [] // AI模式不显示已解答列表
  }
  else {
    // 老师举手列表 - 分为未解答和已解答两组
    raiseHandStudents = cachedTeacherData
    answeredStudents = cachedTeacherAnsweredData
  }
}

/**
 * 获取举手列表数据
 * @param showLoading 是否显示加载状态，默认为false
 * @param isInitialLoad 是否为初始加载，初始加载时即使课程已结束也会请求数据，默认为false
 */
function getTaskStudentRaiseHandListApi(
  showLoading = false,
  isInitialLoad = false,
) {
  // 如果任务已结束且不是初始加载，不再请求数据
  if (isTaskCompleted && !isInitialLoad) {
    isLoading = false
    return
  }

  // 避免重复发起请求，但首次加载时要允许请求（isLoading初始为true）
  if (isLoading && isDataLoaded) return

  // 如果需要显示loading或者是首次加载（isDataLoaded=false）
  if (showLoading || !isDataLoaded) isLoading = true

  getTaskStudentRaiseHandList({
    taskId,
  })
    .then((res) => {
      // 增加计数器，用于更新key
      pollCounter++

      // 缓存AI数据
      cachedAIData = res.aiTaskStudentRaiseHandQueryList || []

      // 缓存老师数据并分类
      cachedTeacherData = []
      cachedTeacherAnsweredData = []

      const teacherList = res.teacherTaskStudentRaiseHandQueryList || []

      teacherList.forEach((item) => {
        // raiseHandState: 1-举手中,2-解答中,3-已解答
        if (item.raiseHandState === 3) cachedTeacherAnsweredData.push(item)

        else cachedTeacherData.push(item)
      })

      // 标记数据已加载
      isDataLoaded = true

      // 更新显示数据
      updateDisplayData()
    })
    .catch((err) => {
      console.error('获取举手列表失败:', err)

      // 发生错误时设置为空数组
      cachedAIData = []
      cachedTeacherData = []
      cachedTeacherAnsweredData = []

      // 标记数据已加载，防止反复请求
      isDataLoaded = true

      // 更新显示数据
      updateDisplayData()
    })
    .finally(() => {
      // 无论成功还是失败，都确保关闭loading状态
      isLoading = false
    })
}

/**
 * 开始轮询获取举手列表数据
 */
function startPolling() {
  // 如果任务已结束或未开始，不启动轮询，但需要获取一次初始数据
  if (isTaskCompleted || !isTaskStarted) {
    // 即使课程已结束，也获取一次初始数据
    getTaskStudentRaiseHandListApi(true, true)
    getStatisticsDataApi() // 获取一次统计数据
    return
  }

  // 如果已有轮询定时器，先清除
  if (pollingTimer !== null) clearInterval(pollingTimer)

  // 设置2秒轮询
  pollingTimer = setInterval(() => {
    // 轮询时不显示loading状态
    getTaskStudentRaiseHandListApi(false)
    getStatisticsDataApi() // 添加统计数据的轮询
  }, 2000) as unknown as number
}

/**
 * 停止轮询
 */
function stopPolling() {
  if (pollingTimer !== null) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

/**
 * 获取任务解答开关和解答上限次数
 */
async function getConfigApi() {
  try {
    await getConfig({
      taskId,
    }).then((res) => {
      // 回填数据：status 1-关，2-开，转换为布尔值
      handChecked = res.status === 2
      // 回填数据：解答上限次数
      answerNum = res.upperCount || 20
      // 保存任务开始时间和结束时间
      releaseTime = res.releaseTime || ''
      requireCompleteTime = res.requireCompleteTime || ''

      // 去掉测试硬编码时间
      // requireCompleteTime = '2025-07-23 11:10:00'
      // releaseTime = '2025-07-23 11:08:00'

      // 初始化倒计时
      initCountdown()

      // 标记初始化完成，并延迟执行以确保监听器不会立即触发
      setTimeout(() => {
        isInitialized = true
      }, 100)
    })
  }
  catch (error) {
    console.log('error', error)
    // 关闭举手列表loading
    isLoading = false
  }
}

/**
 * 更新任务解答配置
 */
function updateConfig() {
  // 如果尚未初始化完成，不进行保存
  if (!isInitialized) return

  // 如果上限次数为空或为0，不进行保存
  if (answerNum === undefined || answerNum === null || answerNum === 0) return

  // 状态：1-关,2-开
  const status = handChecked ? 2 : 1

  // 调用保存配置的API
  setTaskStudentRaiseHandApi({
    taskId,
    status,
    upperCount: answerNum,
  })
    .then(() => {
      // 保存成功提示
      $g.message.success('设置成功')
    })
    .catch((error) => {
      console.error('保存配置失败:', error)
      $g.message.error('设置失败')
    })
}

function handleQuestionApi(item) {
  // 确保taskStudentRaiseHandId存在
  if (!item.taskStudentRaiseHandId) {
    console.error('taskStudentRaiseHandId不存在')
    $g.message.error('解答失败，参数错误')
    return
  }

  // 设置对应举手记录的按钮loading状态为true
  buttonLoadingMap[item.taskStudentRaiseHandId] = true

  handleQuestion({
    taskStudentRaiseHandId: item.taskStudentRaiseHandId,
  })
    .then(() => {
      // 重新请求列表
      getTaskStudentRaiseHandListApi(true, true)
      $g.message.success('解答成功')
    })
    .catch((error) => {
      console.error('解答失败:', error)
      $g.message.error('解答失败，请重试')
      // 接口调用失败时也要刷新一次列表，确保UI状态正确
      getTaskStudentRaiseHandListApi(true, true)
    })
    .finally(() => {
      // 无论成功或失败，都将按钮loading状态重置
      if (item.taskStudentRaiseHandId) buttonLoadingMap[item.taskStudentRaiseHandId] = false
    })
}

// 使用全局lodash的防抖函数
const debouncedUpdateConfig = $g._.debounce(updateConfig, 500)

// 监听器设置
onMounted(async () => {
  getStatisticsDataApi()
  await getConfigApi()
  // 首次加载举手列表数据，显示loading状态，并标记为初始加载
  getTaskStudentRaiseHandListApi(true, true)
  // 开始轮询
  startPolling()

  // 在组件挂载后设置监听器，确保初始化阶段的值变化不会触发保存
  watch([() => handChecked, () => answerNum], () => {
    // 只有初始化完成后才执行保存
    if (isInitialized) debouncedUpdateConfig()
  })

  // 监听举手类型变化，更新显示数据
  watch(
    () => raiseHandType,
    () => {
      updateDisplayData()
    },
  )
})

// 组件销毁时停止轮询
onUnmounted(() => {
  stopPolling()
})

/**
 * 处理进度条全屏状态变化
 * @param value 是否全屏
 */
function handleProgressFullScreenChange(value: boolean) {
  isProgressFullScreen = value
}

const isShowStudentCard = computed(() => {
  return $g.isFlutter || import.meta.env.VITE_APP_ENV === 'development'
})
</script>

<template>
  <div class="p-26px pb-0">
    <g-navbar title="数据看板" class="mb-20px">
      <template #center>
        <div v-if="!$g.isPC" class="flex-cc">
          <!-- 全屏状态下的顶部倒计时 -->
          <FullscreenCountdown
            class="flex relative -top-1px"
            :time="time"
            :time-percentage="timePercentage"
            :is-task-completed="isTaskCompleted"
            :is-task-started="isTaskStarted"
            :countdown-color-class="countdownColorClass"
          />
        </div>
      </template>
    </g-navbar>
    <div v-show="$g.isPC" class="h-51px bg-white rounded-[6px] flex-cc text-[#FFA265] mb-17px">
      <img
        v-if="!isTaskCompleted && countdownColorClass.iconSrc"
        class="w-21px h-21px"
        :src="countdownColorClass.iconSrc"
        alt=""
      />
      <div
        v-if="releaseTime || requireCompleteTime"
        class="ml-10px text-18px flex-cc"
        :class="[countdownColorClass.colorClass]"
      >
        <template v-if="isTaskCompleted">
          课程已结束
        </template>
        <template v-else-if="!isTaskStarted">
          课程将于
          <van-count-down
            :time="time"
            format="mm分ss秒"
            class="text-18px mx-4px"
            :class="[countdownColorClass.colorClass]"
            @change="handleCountdownChange"
          />
          后开始
        </template>
        <template v-else>
          课程将于
          <van-count-down
            :time="time"
            format="mm分ss秒"
            class="text-18px mx-4px"
            :class="[countdownColorClass.colorClass]"
            @change="handleCountdownChange"
          />
          后结束
        </template>
      </div>
    </div>
    <DataPresentation
      :data-option="dataOption"
      class="mb-17px "
      :class="{ '!py-10px': !$g.isPC }"
    >
      <template #finishCatalogCount="{ data }">
        <div class="text-26px font-600">
          {{ data.finishCatalogCount }}/{{ data.totalCatalogCount }}
        </div>
      </template>
      <template #avgLearnDuration="{ data }">
        <div class="text-26px font-600">
          {{ formatSecondsToHourMinute(data.avgLearnDuration).hours }}
          <span class="text-13px">h</span>
          {{ formatSecondsToHourMinute(data.avgLearnDuration).minutes }}
          <span class="text-13px">m</span>
        </div>
      </template>
    </DataPresentation>
    <!-- 主体 -->
    <div class="rounded-[6px] flex h-[calc(100vh-290px)]">
      <div
        class="bg-[#FEFEFE] w-500px flex-shrink-0  overflow-auto"
        :class="[
          isProgressFullScreen
            ? 'fixed top-26px left-26px right-26px bottom-26px z-[9999] rounded-[6px]'
            : 'flex-1 rounded-[6px] relative',
        ]"
      >
        <!-- 全屏状态下的顶部倒计时 -->
        <FullscreenCountdown
          v-show="isProgressFullScreen && (releaseTime || requireCompleteTime)"
          :time="time"
          :time-percentage="timePercentage"
          :is-task-completed="isTaskCompleted"
          :is-task-started="isTaskStarted"
          :countdown-color-class="countdownColorClass"
        />
        <div class="w-[1400px] border">
          1212
        </div>
        <ProgressBar
          :task-id="taskId"
          :is-task-completed="isTaskCompleted"
          :is-task-started="isTaskStarted"
          @fullscreen-change="handleProgressFullScreenChange"
        ></ProgressBar>
      </div>
      <div
        v-if="isShowStudentCard"
        v-show="!isProgressFullScreen"
        :key="raiseHandType"
        class="bg-white rounded-[6px] w-310px ml-17px p-17px pb-6px flex flex-col flex-[0_0_310px]"
      >
        <div class="text-15px font-600">
          学生举手
        </div>
        <div class="flex w-full mt-10px mb-17px">
          <el-radio-group v-model="raiseHandType" class="mx-auto">
            <el-radio-button :value="2">
              老师解答
            </el-radio-button>
            <el-radio-button :value="1">
              AI解答
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="overflow-y-auto flex-1 mb-10px">
          <!-- 加载中状态 - 仅在首次加载且尚未完成时显示 -->
          <div v-if="isLoading && !isDataLoaded" class="py-10px">
            <g-loading class="h-200px" />
          </div>

          <!-- 已加载数据状态 -->
          <template v-else>
            <!-- 举手中的学生 -->
            <template v-if="raiseHandStudents.length > 0">
              <StudentCard
                v-for="student in raiseHandStudents"
                :key="student.taskStudentRaiseHandId"
                :student-info="student"
                :is-a-i-mode="raiseHandType === 1"
                :loading="student.taskStudentRaiseHandId
                  ? buttonLoadingMap[student.taskStudentRaiseHandId]
                  : false
                "
                class="mb-10px"
                @answer="handleQuestionApi"
              ></StudentCard>
            </template>

            <!-- 分隔线和已解答的学生，仅在非AI解答模式下显示 -->
            <template v-if="raiseHandType !== 1 && answeredStudents.length > 0">
              <!-- 已解答分割线 -->
              <div
                class="flex items-center justify-center gap-10px mb-10px"
                :class="{ 'mt-17px': raiseHandStudents.length > 0 }"
              >
                <div
                  class="h-1px flex-1 border-t-[1.5px] border-dashed border-[#E5E5E5]"
                ></div>
                <div class="text-14px text-[#333333]">
                  已解答
                </div>
                <div
                  class="border-t-[1.5px] h-1px flex-1 border-dashed border-[#E5E5E5]"
                ></div>
              </div>

              <!-- 已解答的学生 -->
              <StudentCard
                v-for="student in answeredStudents"
                :key="student.taskStudentRaiseHandId"
                :student-info="student"
                :is-a-i-mode="raiseHandType === 1"
                :answered="true"
                class="mb-10px"
              ></StudentCard>
            </template>

            <!-- 无数据状态 -->
            <div
              v-if="
                raiseHandStudents.length === 0 && answeredStudents.length === 0
              "
              class="flex-cc py-50px text-[#999999]"
            >
              暂无举手数据
            </div>
          </template>
        </div>

        <div
          class="tool-bar h-38px bg-[#F3F4F9] rounded-[4px] mt-auto mb-10px flex items-center px-9px text-15px w-full"
        >
          <div class="mr-2px">
            举手解答
          </div>
          <van-switch v-model="handChecked" size="14px" />
          <div class="fgx"></div>
          <div class="ml-9px mr-8px">
            解答上限
          </div>
          <el-input-number
            v-model="answerNum"
            :min="1"
            :max="99"
            :controls="false"
            class="w-45px mr-8px h-22px"
          ></el-input-number>
          <span class="text-[#666666]">次</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep() {
  .el-radio-button__inner {
    width: 77px;
    height: 25px;
    padding: 0 !important;
    border-radius: 9px 0 0 9px !important;
    color: #999999;
    font-size: 15px;
    font-weight: 400;
    line-height: 25px !important;
    text-align: center;
    background: #F1F1F1;
  }

  .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0 9px 9px 0 !important;
  }
}

.fgx {
  width: 1px;
  height: 16px;
  margin: 0 10px;
  background: #ccc;
  margin-right: 0;
}
</style>
