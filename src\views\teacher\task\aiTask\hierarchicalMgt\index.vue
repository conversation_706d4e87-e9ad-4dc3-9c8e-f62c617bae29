<script setup lang="ts" name="HierarchicalMgt">
import {
  getExamReportDetail,
  getScoreConfig,
  getStudentList,
  getSystemLevelList,
  saveScoreConfigApi,
} from '@/api/aiTask'
import { useAiTaskStore } from '@/stores/modules/aiTask'
import Draggable from 'vuedraggable'
import StudentCard from './components/StudentCard.vue'

const aiTaskStore = useAiTaskStore()
const route = useRoute()

// 分组类型：1-系统分组，2-手动分组
let assignmentObjectState = $ref(1)
let showDialog = $ref(false)
let loading = $ref(false)
let saveLoading = $ref(false)

// 从路由参数获取必要参数
const routeParams = $computed(() => ({
  reportId: route.query.reportId ? Number(route.query.reportId) : null,
  sysSubjectId: Number(route.query.sysSubjectId),
  schoolClassId: Number(route.query.schoolClassId),
}))

// 系统分组数据
let systemLevelData = $ref<Array<{
  layerType: number
  layerTypeName: string
  studentList: Array<{
    schoolStudentId: number
    accountId: number
    idNum: string
    studentName: string
    headPicture: string
    studentScore?: string // 新增学生分数字段
  }>
}>>([])

// 考试信息数据
let examInfo = $ref<{
  examName?: string
  examDate?: string
}>({})

// 考试报告详情数据
let examReportDetail = $ref<{
  examId?: number
  reportId?: number
  examName?: string
  examDate?: string
  totalScore?: string
}>({})

// 手动分组默认数据结构（增加待分组类型）
let manualLevelData = $ref<Array<{
  layerType: number
  layerTypeName: string
  studentList: Array<{
    schoolStudentId: number
    accountId: number
    idNum: string
    studentName: string
    headPicture: string
    studentScore?: string
  }>
}>>([
  {
    layerType: 0,
    layerTypeName: '待分组',
    studentList: [],
  },
  {
    layerType: 1,
    layerTypeName: '高分组',
    studentList: [],
  },
  {
    layerType: 2,
    layerTypeName: '中分组',
    studentList: [],
  },
  {
    layerType: 3,
    layerTypeName: '低分组',
    studentList: [],
  },
])

// 分组分数配置数据
let scoreConfigData = $ref<{
  schoolClassId?: number
  sysSubjectId?: number
  lastTotalScore?: number
  currentTotalScore?: number
  shouldUpdate?: number
  items?: Array<{
    layerType: number
    beginScore: number
    endScore: number
  }>
}>({})

// 分组配置项
let scoreConfigItems = $ref<Array<{
  layerType: number
  beginScore: number
  endScore: number
}>>([])

// 弹窗中使用的临时分组配置（深拷贝）
let tempScoreItems = $ref<Array<{
  layerType: number
  beginScore: number
  endScore: number
}>>([])

// 统一的分组数据 - 根据分组类型返回对应数据
const computedLevelData = $computed(() => {
  if (assignmentObjectState === 1) {
    return systemLevelData
  }
  else {
    // 手动分组：直接返回manualLevelData，保持引用一致性
    return manualLevelData
  }
})

// 获取分组显示名称的计算属性
function getLayerDisplayName(layer: any) {
  if (layer.layerType === 0)
    return layer.layerTypeName // 待分组保持原有名称

  // 手动分组模式下，只显示分组名称
  if (assignmentObjectState === 2) {
    const layerNames = {
      1: '高分组',
      2: '中分组',
      3: '低分组',
    }
    return layerNames[layer.layerType] || layer.layerTypeName
  }

  // 系统分组模式下，显示分组名称和分数范围
  const scoreItem = scoreConfigItems.find(item => item.layerType === layer.layerType)
  if (scoreItem) {
    const layerNames = {
      1: '高分组',
      2: '中分组',
      3: '低分组',
    }
    return `${layerNames[layer.layerType]}（${scoreItem.beginScore}~${scoreItem.endScore}分）`
  }

  // 兜底显示
  return layer.layerTypeName
}

const info = $computed(() => {
  // 动态计算待分组学生数量
  const waitingGroupStudentCount = assignmentObjectState === 2
    ? manualLevelData.find(layer => layer.layerType === 0)?.studentList.length || 0
    : 0

  const map = {
    1: examInfo.examName && examInfo.examDate
      ? `最新分组数据更新于考试时间${examInfo.examDate}的${examInfo.examName}`
      : '',
    2: waitingGroupStudentCount > 0
      ? `*目前尚有${waitingGroupStudentCount}名学生待分组安排`
      : '*所有学生已完成分组安排',
  }
  // 无考试数据时 显示无考试数据
  // 学生全部配对完成时 返回空字符串
  return map[assignmentObjectState]
})

function openDialog() {
  // 深拷贝分组配置数据到临时变量
  tempScoreItems = $g._.cloneDeep(scoreConfigItems)
  showDialog = true
}

/**
 * 获取考试报告详情
 */
async function getExamReportDetailApi() {
  try {
    const res = await getExamReportDetail({
      reportId: routeParams.reportId,
    })

    if (res)
      examReportDetail = res
  }
  catch (error) {
    console.error('获取考试报告详情失败：', error)
  }
}

/**
 * 获取分组分数配置
 */
async function getScoreConfigApi() {
  try {
    const res = await getScoreConfig({
      schoolClassId: routeParams.schoolClassId,
      sysSubjectId: routeParams.sysSubjectId,
      reportId: routeParams.reportId, // 提供默认值
    })

    if (res) {
      // 存储完整的配置数据
      scoreConfigData = res

      // 提取分组配置项
      scoreConfigItems = res.items || []

      // 如果需要更新，自动打开弹窗
      if (res.shouldUpdate === 2)
        openDialog()
    }
  }
  catch (error) {
    console.error('获取分组分数配置失败：', error)
  }
}

/**
 * 获取系统分组列表
 */
async function getSystemLevelListApi() {
  try {
    loading = true
    const res = await getSystemLevelList({
      schoolClassId: routeParams.schoolClassId,
      sysSubjectId: routeParams.sysSubjectId,
      reportId: routeParams.reportId,
    })

    // 根据新的返回结构，从 layerList 中获取分组数据
    if (res && res.layerList) {
      systemLevelData = res.layerList

      // 保存考试信息用于显示
      examInfo.examName = res.examName
      examInfo.examDate = res.examDate
    }
    else {
      systemLevelData = []
    }
  }
  catch (error) {
    console.error('获取系统分组列表失败：', error)
    systemLevelData = []
  }
  finally {
    loading = false
  }
}

/**
 * 获取班级学生列表（用于手动分组）
 */
async function getStudentListApi() {
  try {
    const res = await getStudentList({
      schoolClassId: routeParams.schoolClassId,
    })

    // 将学生列表分配到待分组中
    if (res && Array.isArray(res)) {
      const waitingGroup = manualLevelData.find(layer => layer.layerType === 0)
      if (waitingGroup)
        waitingGroup.studentList = res
    }
  }
  catch (error) {
    console.error('获取班级学生列表失败：', error)
  }
}

/**
 * 重置页面数据到初始状态
 */
function resetPageData() {
  // 重置系统分组数据
  systemLevelData = []

  // 重置考试信息
  examInfo = {}

  // 重置考试报告详情
  examReportDetail = {}

  // 重置手动分组数据到初始结构
  manualLevelData = [
    {
      layerType: 0,
      layerTypeName: '待分组',
      studentList: [],
    },
    {
      layerType: 1,
      layerTypeName: '高分组',
      studentList: [],
    },
    {
      layerType: 2,
      layerTypeName: '中分组',
      studentList: [],
    },
    {
      layerType: 3,
      layerTypeName: '低分组',
      studentList: [],
    },
  ]

  // 重置分组分数配置数据
  scoreConfigData = {}

  // 重置分组配置项
  scoreConfigItems = []

  // 重置临时分组配置
  tempScoreItems = []

  // 清空 store 中的学生分层数据
  aiTaskStore.studentLevelData = []

  // 重置弹窗状态
  showDialog = false

  // 重置保存加载状态
  saveLoading = false
}

/**
 * 初始化页面数据
 */
async function initPageData() {
  try {
    loading = true

    // 首先重置所有数据到初始状态
    resetPageData()

    // 根据路由参数判断分组类型，如果有reportId则默认系统分组，否则手动分组
    if (routeParams.reportId) {
      assignmentObjectState = 1
      await getExamReportDetailApi()
      await getSystemLevelListApi()
      await getScoreConfigApi()
    }
    else {
      // reportId为空，默认选中手动分组，不请求分数配置信息
      assignmentObjectState = 2
    }

    // 获取班级学生列表（用于手动分组）
    await getStudentListApi()
  }
  catch (error) {
    console.error('初始化页面数据失败：', error)
  }
  finally {
    loading = false
  }
}

// 监听 computedLevelData 变化，自动更新 store 中的 studentLevelData
watch(() => computedLevelData, (newData) => {
  // 深拷贝数据避免引用问题
  aiTaskStore.studentLevelData = $g._.cloneDeep(newData)
}, {
  deep: true,
  immediate: true,
})

/**
 * 重置手动分组数据
 */
function resetManualLevelData() {
  // 重置手动分组数据到初始结构
  manualLevelData = [
    {
      layerType: 0,
      layerTypeName: '待分组',
      studentList: [],
    },
    {
      layerType: 1,
      layerTypeName: '高分组',
      studentList: [],
    },
    {
      layerType: 2,
      layerTypeName: '中分组',
      studentList: [],
    },
    {
      layerType: 3,
      layerTypeName: '低分组',
      studentList: [],
    },
  ]

  // 重新获取学生列表并分配到待分组
  getStudentListApi()
}

// 监听分组类型变化
watch(() => assignmentObjectState, (newValue) => {
  // 如果用户手动切换到系统分组，调用系统分组API
  if (newValue === 1) {
    if (routeParams.reportId)
      getExamReportDetailApi()
    getSystemLevelListApi()
    getScoreConfigApi()
  }

  // 如果切换到手动分组，重置手动分组数据
  else if (newValue === 2) {
    resetManualLevelData()
  }
})

/**
 * 保存分组配置
 */
async function saveScoreConfig() {
  try {
    saveLoading = true

    // 调用保存接口
    await saveScoreConfigApi({
      schoolClassId: routeParams.schoolClassId,
      sysSubjectId: routeParams.sysSubjectId,
      reportId: routeParams.reportId,
      items: tempScoreItems,
    })

    // 将临时配置应用到正式配置
    scoreConfigItems = $g._.cloneDeep(tempScoreItems)

    // 关闭弹窗
    showDialog = false

    // 重新获取系统分组数据
    if (assignmentObjectState === 1)
      await getSystemLevelListApi()

    console.log('分组配置保存成功')
  }
  catch (error) {
    console.error('保存分组配置失败：', error)
  }
  finally {
    saveLoading = false
  }
}

// 取消分组配置修改
function cancelScoreConfig() {
  showDialog = false
}

// 拖拽事件处理函数
function handleDragStart() {
  // 拖拽开始时的处理逻辑（如果需要的话）
}

function handleDragEnd() {
  // 拖拽结束时的处理逻辑（如果需要的话）
}

function handleDragChange() {
  // 拖拽数据变化时的处理逻辑（如果需要的话）
}

/**
 * 数据同步：将系统分组数据同步到手动分组
 */
function handleDataSync() {
  try {
    // 检查系统分组数据是否存在
    if (!systemLevelData || systemLevelData.length === 0) {
      $g.showToast('暂无系统分组数据可同步')
      return
    }

    // 重置手动分组数据结构
    manualLevelData[0].studentList = []

    // 将系统分组数据同步到手动分组对应的分组中
    systemLevelData.forEach((systemLayer) => {
      const targetLayer = manualLevelData.find(layer => layer.layerType === systemLayer.layerType)
      if (targetLayer && systemLayer.studentList) {
        // 深拷贝学生数据，避免引用问题
        targetLayer.studentList = $g._.cloneDeep(systemLayer.studentList)
      }
    })
    console.log('⚡[ manualLevelData ] >', manualLevelData)
    // 显示同步成功提示
    $g.showToast('同步成功')

    console.log('系统分组数据已同步到手动分组')
  }
  catch (error) {
    console.error('数据同步失败：', error)
    $g.showToast('同步失败，请重试')
  }
}

onMounted(() => {
  initPageData()
})
// onActivated(() => {
//   if (!aiTaskStore.studentLevelData?.length)
//     initPageData()
// })
</script>

<template>
  <div class="p-26px pb-0">
    <g-navbar title="布置课程任务" class="mb-26px"></g-navbar>
    <!-- <div class="bg-white rounded-[6px] p-17px mb-26px">
      <div class="font-600">
        分组设置
      </div>
      <div class="flex items-center ">
        <span class="mr-20px">布置对象</span>
        <el-radio-group v-model="assignmentObjectState" class="w-300px">
          <el-radio :value="1" :disabled="!routeParams.reportId">
            系统分组
          </el-radio>
          <el-radio :value="2">
            手动分组
          </el-radio>
        </el-radio-group>
      </div>
    </div> -->
    <div class="fc-body">
      <div class="flex justify-between  items-center mb-17px">
        <div>
          <div class="font-600 mb-18px flex justify-between items-center">
            分组名单
            <div class="flex items-center ml-10px">
              <el-radio-group v-model="assignmentObjectState" class="w-300px">
                <el-radio :value="1" :disabled="!routeParams.reportId">
                  系统分组
                </el-radio>
                <el-radio :value="2">
                  手动分组
                </el-radio>
              </el-radio-group>
            </div>
          </div>
          <div
            class="text-[#6C6C74] text-11px"
          >
            {{ info }}<span
              v-show="assignmentObjectState === 1"
              class="text-theme-primary cursor-pointer font-600 ml-8px"
              @click="handleDataSync"
            >
              数据同步
            </span>
          </div>
        </div>
        <el-button
          v-if="assignmentObjectState === 1"
          class="w-94px h-34px bg-[#E9EBFB] text-theme-primary border-theme-primary"
          @click="openDialog"
        >
          分数设置
        </el-button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center ">
        <g-loading class="h-[200px]" />
      </div>

      <!-- 统一的分组内容模板 -->
      <div v-else class="flex gap-17px h-[calc(100%-70px)]">
        <!-- 有分组数据时渲染 -->
        <template v-if="computedLevelData.length > 0">
          <div
            v-for="layer in computedLevelData"
            v-show="!(layer.layerType === 0 && layer.studentList.length === 0)"
            :key="layer.layerType"
            class="fc-item"
            :class="{ 'waiting-group': layer.layerType === 0, 'pr-4px pl-8px': assignmentObjectState === 2 }"
          >
            <div class="flex justify-between items-center mb-13px h-28px">
              <div class="fc-item-title">
                {{ getLayerDisplayName(layer) }}（{{ layer.studentList.length }}人）
              </div>
            </div>
            <div class="h-[calc(100%-35px)] overflow-auto">
              <!-- 使用Draggable组件包装学生列表 -->
              <Draggable
                v-model="layer.studentList"
                :group="{
                  name: 'students',
                  pull: assignmentObjectState === 2,
                  put: assignmentObjectState === 2,
                }"
                :disabled="assignmentObjectState === 1"
                :animation="150"
                ghost-class="ghost-class"
                chosen-class="chosen-class"
                drag-class="drag-class"
                item-key="schoolStudentId"
                :class="[
                  `drag-container layer-${layer.layerType}`,
                  { 'drag-disabled': assignmentObjectState === 1 },
                ]"
                :force-fallback="true"
                :fallback-tolerance="0"
                :empty-insert-threshold="10"
                :delay="$g.isPC ? 0 : 50"
                :delay-on-touch-start="!$g.isPC"
                :touch-start-threshold="$g.isPC ? 0 : 10"
                @start="handleDragStart"
                @end="handleDragEnd"
                @change="handleDragChange"
              >
                <template #item="{ element: student }">
                  <StudentCard
                    :key="student.schoolStudentId"
                    :student="student"
                    :show-drag-icon="assignmentObjectState === 2"
                  />
                </template>
              </Draggable>
            </div>
          </div>
        </template>

        <!-- 无分组数据时显示空状态 -->
        <template v-else>
          <div class="w-full flex justify-center items-center">
            <g-empty :description="assignmentObjectState === 1 ? '暂无系统分组数据' : '暂无手动分组数据'" />
          </div>
        </template>
      </div>
    </div>
    <van-popup
      v-model:show="showDialog"
      class="custom-van-popup"
    >
      <div class="text-17px font-500  flex-cc mb-17px">
        分组范围设置
      </div>
      <div class="text-14px text-[#6C6C74] mb-20px mx-30px">
        * 最新考试数据来源于<span class="text-theme-danger">{{ examReportDetail.examDate }}</span> 的{{ examReportDetail.examName }}。该场考试总分值为<span class="text-theme-danger">{{ examReportDetail.totalScore }} 分</span>，具体分组内容如下：
      </div>
      <div
        v-for="(item, index) in tempScoreItems"
        :key="index"
        class="flex items-center justify-between mb-17px mx-auto"
      >
        <div class="flex items-center">
          <div class="w-60px h-28px flex items-center justify-start font-500 mr-10px">
            {{ item.layerType === 1 ? '高分组' : item.layerType === 2 ? '中分组' : '低分组' }}：
          </div>

          <!-- 统一宽度的内容区域 -->
          <div class="w-200px flex items-center justify-center">
            <!-- 高分组：≥ [输入框] 分 -->
            <template v-if="item.layerType === 1">
              <span class="mr-8px">≥</span>
              <el-input-number
                v-model="item.beginScore"
                class="w-70px mr-4px"
                :min="0"
                :controls="false"
                :precision="0"
              ></el-input-number>
              <span>分</span>
            </template>

            <!-- 中分组：[输入框] 分 ~ [输入框] 分 -->
            <template v-else-if="item.layerType === 2">
              <el-input-number
                v-model="item.beginScore"
                class="w-70px mr-4px"
                :min="tempScoreItems.find(i => i.layerType === 3)?.endScore || 0"
                :max="tempScoreItems.find(i => i.layerType === 1)?.beginScore"
                :controls="false"
                :precision="0"
              ></el-input-number>
              <span class="mr-8px">分</span>
              <span class="mx-8px">~</span>
              <el-input-number
                v-model="item.endScore"
                class="w-70px mr-4px"
                :min="tempScoreItems.find(i => i.layerType === 3)?.endScore || 0"
                :max="tempScoreItems.find(i => i.layerType === 1)?.beginScore || 100"
                :controls="false"
                :precision="0"
              ></el-input-number>
              <span>分</span>
            </template>

            <!-- 低分组：≤ [输入框] 分 -->
            <template v-else>
              <span class="mr-8px">≤</span>
              <el-input-number
                v-model="item.endScore"
                class="w-70px mr-4px"
                :min="0"
                :controls="false"
                :precision="0"
              ></el-input-number>
              <span>分</span>
            </template>
          </div>
        </div>
      </div>
      <div class="mx-auto mt-15px">
        <el-button
          class="w-75px h-30px bg-[#F4F4F4]  border-[#F4F4F4] mr-64px"
          @click="cancelScoreConfig"
        >
          取消
        </el-button>
        <el-button
          class="w-75px h-30px bg-[#6474FD] text-white border-theme-primary"
          :loading="saveLoading"
          @click="saveScoreConfig"
        >
          确定
        </el-button>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
.fc-body {
  height: calc(100vh - 100px);
  padding: 17px;
  border: 1px solid #FFF;
  border-radius: 17px;
  background: rgb(255 255 255 / 40%);
  .fc-item {
    width: 100%;
    height: 100%;
    padding: 17px;
    border-radius: 16px;
    background: #FFF;

    .fc-item-title{
      display: flex;
      align-items: center;
      font-weight: 600;
    }

    // 待分组容器样式
    &.waiting-group {
      border: 2px dashed #6474FD;

      // background: #FFFBF0;
    }
  }
}

.custom-van-popup {
  @apply flex flex-col overflow-auto pt-21px pb-26px w-[430px] min-h-[360px] br-[13px] bg-gradient-to-b from-[#E5E8FF] via-[80px] via-[#ffffff];
  :deep() {
    .van-icon-cross {
      top: 18px;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }
  }
}

// 拖拽样式
:deep() {
  .el-radio {
    margin-right: 17px;
  }

  // 拖拽容器样式
  .drag-container {
    min-height: 200px;
    padding: 2px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    user-select: none; // 防止拖拽时选中文字
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // 当有拖拽元素悬停时的样式
    &.sortable-ghost-active {
      background-color: rgb(100 116 253 / 10%);
    }

    // 禁用拖拽时的样式
    &.drag-disabled {
      cursor: default !important;

      // 禁用拖拽时，子元素也不可拖拽
      * {
        cursor: default !important;
        pointer-events: auto;
      }
    }
  }

  // 空状态占位区域样式
  .drag-empty-area {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    min-height: 150px;
    background-color: #fafafa;
    transition: all 0.2s ease;

    &:hover {
      border-color: #6474FD;
      background-color: rgb(100 116 253 / 5%);
    }
  }

  // 拖拽时的幽灵元素样式
  .ghost-class {
    border-radius: 11px;
    background: #c8ebfb;
    opacity: 0.5;
  }

  // 选中时的样式 - 多种选择器确保生效
  .chosen-class,
  .sortable-chosen {
    outline: 2px dashed #6474FD !important;
    border-radius: 11px !important;
    box-shadow: 0 0 0 2px rgb(100 116 253 / 30%) !important;
  }

  // 拖拽过程中的样式
  .drag-class,
  .sortable-drag {
    outline: 2px dashed #6474FD !important;
    border-radius: 11px !important;
    opacity: 0.8 !important;
  }

  // 拖拽目标区域高亮
  .sortable-dragover {
    background-color: rgb(100 116 253 / 10%) !important;
    border-radius: 8px !important;
  }
}
</style>
