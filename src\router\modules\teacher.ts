import Layout from '@/layout/index.vue'
import { KeepAlive } from 'vue'

/**
 * meta.fullPage  默认所有都为false，在permissions.ts中取反
 * meta.debugQuery  从debugging 页面点击跳转时，可以携带的query参数，便于调试
 * meta.keepAliveArr  跳转到指定页面时，对本页面进行缓存,值为指定页面的路由name组成的数组
 * meta.hidden  是否隐藏菜单，默认false
 */
export default [
  {
    path: '/teacher',
    name: 'Teacher',
    component: Layout,
    meta: {
      title: '老师用户端',
    },
    children: [
      {
        path: 'courseSchedule',
        name: 'CourseSchedule',
        meta: {
          title: '课程表',
          showMenu: true,
          icon: 'schedule',
        },
        redirect: { name: 'CourseMain' },
        children: [{
          path: 'courseMain',
          name: 'CourseMain',
          meta: {
            title: '课程表首页',
            fullPage: true,
            hidden: true,
          },
          component: () => import('@/views/teacher/courseSchedule/index.vue'),
        },
        ],
      },
      {
        path: 'taskCenter',
        name: 'TaskCenter',
        meta: {
          title: '任务中心',
          showMenu: true,
        },
        redirect: { name: 'TeacherHome' },
        children: [
          {
            path: 'home',
            name: 'TeacherHome',
            meta: {
              title: '首页',
              hidden: true,
              fullPage: true,
              keepAliveArr: ['CreateTask',
'GroupManage',
'ResourceReport',
'QuestionReport',
'ErrorTask',
'ComprehensiveTask',
'NormalTaskStudentList',
'AiTaskList'],
            },
            component: () => import('@/views/teacher/home/<USER>'),
          },
          {
            path: 'createTask',
            name: 'CreateTask',
            meta: {
              title: '创建任务',
              hidden: true,
            },
            component: () =>
              import('@/views/teacher/task/createTask/index.vue'),
          },
          {
            path: 'errorTask',
            name: 'ErrorTask',
            meta: {
              title: '错题任务',
              hidden: true,
              keepAliveArr: ['ErrorQuestionDetail'],
            },
            component: () =>
              import('@/views/teacher/task/errorTaskNew/index.vue'),

          },
          {
            path: 'report',
            name: 'Report',
            meta: {
              title: '报告',
              hidden: true,
            },
            children: [
              {
                path: 'questionReport',
                name: 'QuestionReport',
                meta: {
                  title: '试题类报告',
                  debugQuery: {
                    exerciseSourceType: 2,
                    exerciseSourceId: 320,
                    schoolId: 10,
                    title: '测试跳转',
                  },
                  keepAliveArr: ['QuestionDetail',
'CorrectionDetail',
'ReportQuestionList'],
                },
                component: () => import('@/views/teacher/task/questionReport/index.vue'),
              },
              {
                path: 'reportQuestionList',
                name: 'ReportQuestionList',
                component: () => import('@/views/teacher/task/reportQuestionList/index.vue'),
                meta: {
                  title: '个人答题结果',
                },
              },
              {
                path: 'questionDetail',
                name: 'QuestionDetail',
                meta: {
                  title: '试题详情',
                },
                component: () => import('@/views/teacher/task/questionDetail/index.vue'),
              },
              {
                path: 'personalReportDetail',
                name: 'PersonalReportDetail',
                meta: {
                  title: '个人报告-题目详情',
                  keepAliveArr: ['CorrectionPage'],
                },
                component: () => import('@/views/teacher/task/questionDetail/personalReportDetail/index.vue'),
              },
              {
                path: 'correctionDetail',
                name: 'CorrectionDetail',
                meta: {
                  title: '订正详情',
                },
                component: () => import('@/views/teacher/task/questionDetail/correctionDetail/index.vue'),
              },
              {
                path: 'resourceReport',
                name: 'ResourceReport',
                meta: {
                  title: '资源报告',
                  debugQuery: {
                    taskId: 83,
                    taskName: '测试跳转',
                  },
                  fullPage: true,
                },
                component: () => import('@/views/teacher/task/resourceReport/index.vue'),
              },
              {
                path: 'errorQuestionDetail',
                name: 'ErrorQuestionDetail',
                meta: {
                  title: '错题详情',
                },
                component: () => import('@/views/teacher/task/errorTask/questionDetail/index.vue'),
              },
              {
                path: 'correctionPage',
                name: 'CorrectionPage',
                meta: {
                  title: '批改页面',
                },
                component: () => import('@/views/teacher/task/correctionPage/index.vue'),
              },
            ],
          },
          {
            path: 'comprehensiveTask',
            name: 'ComprehensiveTask',
            meta: {
              title: '综合任务',
              hidden: true,
              keepAliveArr: ['ComprehensiveTaskTaskOverview', 'ComprehensiveTaskCreate'],
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/index.vue'),
          },
          {
            path: 'comprehensiveTaskCreate',
            name: 'ComprehensiveTaskCreate',
            meta: {
              title: '创建任务',
              hidden: true,
              keepAliveArr: ['ComprehensiveTaskPackage', 'ComprehensiveTaskTaskOverview'],
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/create/index.vue'),
          },
          {
            path: 'comprehensiveTaskPackage',
            name: 'ComprehensiveTaskPackage',
            meta: {
              title: '子任务包',
              hidden: true,
              keepAliveArr: ['CreateTask'],
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/childPackage/index.vue'),
          },
          {
            path: 'comprehensiveTaskTaskOverview',
            name: 'ComprehensiveTaskTaskOverview',
            meta: {
              title: '任务概况',
              hidden: true,
              keepAliveArr: ['CorrectPage', 'CorrectionPage'],
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/taskOverview/index.vue'),
          },
          {
            path: 'taskReport',
            name: 'TaskReport',
            meta: {
              title: '举一反一报告页',
              keepAliveArr: ['CorrectPage',
'CorrectionPage',
'ComprehensiveTaskTaskOverview'],
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/taskReport/index.vue'),
          },
          {
            path: 'correctPage',
            name: 'CorrectPage',
            meta: {
              title: '默写批改',
              hidden: true,
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/correctPage/index.vue'),
          },
          {
            path: 'normalTaskStudentList',
            name: 'NormalTaskStudentList',
            meta: {
              title: '普通任务学生列表',
              hidden: true,
            },
            component: () =>
              import('@/views/teacher/task/comprehensiveTask/normalTaskStudentList/index.vue'),
          },
          {
            path: 'aiTask',
            name: 'AiTask',
            meta: {
              title: 'AI课程任务',
              hidden: true,
            },
            children: [
              {
                path: 'list',
                name: 'AiTaskList',
                meta: {
                  title: '任务列表',
                },
                component: () => import('@/views/teacher/task/aiTask/task/index.vue'),
              },
              {
                path: 'createAiTask',
                name: 'CreateAiTask',
                meta: {
                  title: '创建AI任务',
                  keepAliveArr: ['AiTaskEdit', 'HierarchicalMgt'],
                },
                component: () => import('@/views/teacher/task/aiTask/task/createTask/index.vue'),
              },
              {
                path: 'courseEdit',
                name: 'AiTaskEdit',
                meta: {
                  title: '课程编辑',
                  keepAliveArr: ['AiTaskVideoPreview'],
                },
                component: () => import('@/views/teacher/task/aiTask/courseEdit/index.vue'),
              },
              {
                path: 'videoPreview',
                name: 'AiTaskVideoPreview',
                meta: {
                  title: '视频预览',
                },
                component: () => import('@/views/teacher/task/aiTask/courseEdit/videoPreview/index.vue'),
              },
              {
                path: 'dataMonitor',
                name: 'AiTaskDataEdit',
                meta: {
                  title: '数据看板',
                },
                component: () => import('@/views/teacher/task/aiTask/dataMonitor/index.vue'),
              },
              {
                path: 'studentReport',
                name: 'AiTaskStudentReport',
                component: () => import('@/views/teacher/task/aiTask/studentReport/index.vue'),
                meta: {
                  title: '学生报告',
                  keepAliveArr: ['AiTaskDataEdit', 'QuestionDetail'],
                },
              },
              {
                path: 'leaveMsg',
                name: 'AiTaskLeaveMsg',
                meta: {
                  title: '课程留言',
                },
                component: () => import('@/views/teacher/task/aiTask/courseEdit/leaveMsg/index.vue'),
              },
              {
                path: 'hierarchicalMgt',
                name: 'HierarchicalMgt',
                meta: {
                  title: '分层管理',
                  hidden: true,
                  keepAliveArr: ['CreateAiTask'],
                },
                component: () => import('@/views/teacher/task/aiTask/hierarchicalMgt/index.vue'),
              },
            ],
          },
        ],
      },
      {
        path: 'ccyReport',
        redirect: { name: 'ActivityList' },
        meta: {
          title: '活动查看',
          fullPage: true,
        },
        children: [
          {
            path: 'activityList',
            name: 'ActivityList',
            meta: {
              title: '活动列表',
              fullPage: true,
              keepAliveArr: ['ReportDetail'],
            },
            component: () => import('@/views/teacher/ccyReport/index.vue'),
          },
          {
            path: 'reportDetail',
            name: 'ReportDetail',
            meta: {
              title: '报告详情',
              fullPage: true,
              keepAliveArr: ['ModuleStatistics',
'ChapterReportDetail',
'PaperDetail',
'CorrectionReport'],
            },
            component: () =>
              import('@/views/teacher/ccyReport/reportDetail/index.vue'),
          },
          {
            path: 'moduleStatistics',
            name: 'ModuleStatistics',
            meta: {
              title: '模块统计',
              fullPage: true,
              keepAliveArr: ['AiCorrectionDetail'],
            },
            component: () =>
              import('@/views/teacher/ccyReport/moduleStatistics/index.vue'),
          },
          {
            path: 'aiCorrectionDetail',
            name: 'AiCorrectionDetail',
            meta: {
              title: 'AI批改详情',
              fullPage: true,
            },
            component: () =>
              import('@/views/teacher/ccyReport/correctionDetail/index.vue'),
          },
          {
            path: 'chapterReportDetail',
            name: 'ChapterReportDetail',
            meta: {
              title: '章节报告详情',
              fullPage: true,
              keepAliveArr: ['QuestionReport'],
            },
            component: () =>
              import('@/views/teacher/ccyReport/reportDetail/chapterReportDetail/index.vue'),
          },
          {
            path: 'paperDetail',
            name: 'PaperDetail',
            meta: {
              title: '月考和周考详情',
              fullPage: true,
              keepAliveArr: ['ExamQViewEdit'],
            },
            component: () =>
              import('@/views/teacher/ccyReport/reportDetail/stageTestDetail/paperDetail/index.vue'),
          },
          {
            path: 'examQViewEdit',
            name: 'ExamQViewEdit',
            meta: {
              title: '试卷浏览和编辑',
              fullPage: true,
            },
            component: () => import('@/views/teacher/ccyReport/reportDetail/stageTestDetail/examQViewEdit/index.vue'),
          },
          {
            path: 'correctionReport',
            name: 'CorrectionReport',
            meta: {
              title: '订正报告',
              fullPage: true,
            },
            component: () => import('@/views/teacher/ccyReport/reportDetail/correctionReport/detail/index.vue'),
          },
        ],
      },
      {
        path: 'syncClass',
        name: 'SyncClass',
        meta: {
          title: '同步课堂',
          fullPage: true,
          showMenu: true,
        },
        redirect: { name: 'SyncClassIndex' },
        children: [
          {
            path: 'index',
            name: 'SyncClassIndex',
            meta: {
              title: '同步课堂',
              fullPage: true,
              hidden: true,
              keepAliveArr: ['AfterTraining', 'CourseLearning'],
            },
            component: () => import('@/views/teacher/syncClass/index.vue'),
          },
          {
            path: 'afterTraining',
            name: 'AfterTraining',
            meta: {
              title: '课后训练',
              hidden: true,
              debugQuery: {
                bookCatalogId: 79725,
                bookCatalogName: '1.2 集合间的基本关系',
                schoolClassId: 26525,

              },
            },
            component: () => import('@/views/teacher/syncClass/afterTraining/index.vue'),
          },
          {
            path: 'courseLearning',
            name: 'CourseLearning',
            meta: {
              title: '课程学习',
              hidden: true,
            },
            component: () => import('@/views/teacher/syncClass/courseLearning/index.vue'),
          },
        ],
      },
      {
        path: 'teachingTools',
        name: 'TeachingTools',
        meta: {
          title: '教学工具',
          showMenu: true,
          fullPage: true,
        },
        children: [
          {
            path: 'groupManage',
            name: 'GroupManage',
            meta: {
              title: '群组管理',
              fullPage: true,
              showMenu: true,
              activeName: 'GroupManage',
            },
            component: () => import('@/views/teacher/groupManage/index.vue'),
          },
          {
            path: 'tools',
            name: 'Tools',
            meta: {
              title: '教学工具',
              fullPage: true,
              showMenu: true,
              activeName: 'Tools',
            },
            component: () => import('@/views/teacher/teachingTools/tools/index.vue'),
          },
          {
            path: 'monitor',
            name: 'Monitor',
            meta: {
              title: '平板监控',
              fullPage: true,
              showMenu: true,
              activeName: 'Monitor',
            },
            component: () => import('@/views/teacher/teachingTools/monitor/index.vue'),
          },
          {
            path: 'moduleSwitch',
            name: 'ModuleSwitch',
            meta: {
              title: '模块开关',
              fullPage: true,
              showMenu: true,
              activeName: 'ModuleSwitch',
            },
            component: () => import('@/views/teacher/teachingTools/moduleSwitch/index.vue'),
          },
        ],
      },

      {
        path: 'classSchedule',
        name: 'ClassSchedule',
        meta: {
          title: '班级课程表管理',
          roles: [2],
          fullPage: true,
          showMenu: true,
          icon: 'schedule-mgt',
        },
        redirect: { name: 'ClassScheduleMain' },
        children: [
          {
            path: 'main',
            name: 'ClassScheduleMain',
            meta: {
              title: '班级课程表',
              keepAliveArr: ['ScheduleMgt'],
              hidden: true,
            },
            component: () => import('@/views/teacher/classSchedule/index.vue'),
          },
          {
            path: 'scheduleMgt',
            name: 'ScheduleMgt',
            component: () =>
              import('@/views/teacher/classSchedule/scheduleMgt/index.vue'),
            meta: {
              title: '课表管理',
              hidden: true,
              activeName: 'ClassSchedule',
            },
          },
        ],
      },

    ],
  },
]
